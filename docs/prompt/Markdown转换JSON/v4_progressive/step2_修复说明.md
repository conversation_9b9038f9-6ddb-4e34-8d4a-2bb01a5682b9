# Step2 提示词修复说明

## 修复背景

基于对`step2_basic_widgets.md`提示词和`s2.json`生成结果的对比分析，发现以下主要问题：

1. **LIST控件被错误处理** - 违反了Step 2只处理TITLE和TEXT的规定
2. **序列编号不连续** - 出现编号跳跃，不符合连续性原则  
3. **内容处理不完整** - 部分TEXT类型内容被遗漏

## 修复内容

### 1. 强化处理范围限制

**修复前**：
```markdown
### 本步骤不处理的类型
- **LIST控件**：留待Step 3处理
- **TABLE控件**：留待Step 4处理
- **CHART控件**：留待Step 5处理
```

**修复后**：
```markdown
### 本步骤严格不处理的类型（重要！）
- **LIST控件**：绝对不能处理，必须留待Step 3处理
- **TABLE控件**：绝对不能处理，必须留待Step 4处理  
- **CHART控件**：绝对不能处理，必须留待Step 5处理

### 处理原则
- **严格限制**：只能生成TITLE和TEXT控件，不得生成其他任何类型的控件
- **完整处理**：必须处理所有推荐为TITLE和TEXT的内容片段，不得遗漏
```

### 2. 优化处理流程

**修复前**：
```markdown
### 1. 输入验证
- 验证Step 1输出的数据结构完整性
- 确认content_segments中的推荐信息
- 识别需要本步骤处理的内容片段
```

**修复后**：
```markdown
### 1. 输入验证与内容筛选
- 验证Step 1输出的数据结构完整性
- 确认content_segments中的推荐信息
- **严格筛选**：只识别推荐为TITLE和TEXT类型的内容片段
- **排除其他类型**：将LIST、TABLE、CHART类型的片段直接保留到remaining_segments
```

### 3. 强化序列编号规则

**修复前**：
```markdown
- **连续性原则**：确保编号按层级结构连续递增，为后续步骤建立正确的编号基础
```

**修复后**：
```markdown
- **连续性原则**：编号必须连续，不得跳跃（如不能从"1.1"直接跳到"1.3"）
- **层级对应**：每个层级的编号都要与内容结构严格对应
```

### 4. 更新核心执行要求

**修复前**：
```markdown
1. **推荐验证**：对Step 1的推荐进行二次验证，确保合理性
2. **类型调整**：根据详细分析结果调整不合理的推荐
3. **控件生成**：为TITLE和TEXT类型生成标准控件
```

**修复后**：
```markdown
1. **严格类型限制**：只能生成TITLE和TEXT控件，绝对不能生成LIST、TABLE、CHART控件
2. **完整内容处理**：必须处理所有推荐为TITLE和TEXT的内容片段，不得遗漏
3. **推荐验证**：对Step 1的TITLE和TEXT推荐进行二次验证，确保合理性
4. **序列编号连续性**：确保编号严格按层级连续递增，不得跳跃
```

### 5. 新增执行前检查清单

**新增内容**：
```markdown
### 执行前检查清单（必须严格遵守！）

在开始处理前，请确认以下要点：

1. **类型限制检查**：
   - ✅ 只生成TITLE和TEXT控件
   - ❌ 绝对不生成LIST、TABLE、CHART控件

2. **内容完整性检查**：
   - ✅ 处理所有推荐为TITLE的内容片段
   - ✅ 处理所有推荐为TEXT的内容片段
   - ❌ 不遗漏任何应处理的内容

3. **序列编号检查**：
   - ✅ 编号必须连续（1, 2, 3 或 1.1, 1.2, 1.3）
   - ❌ 不得出现跳跃（如1.1直接跳到1.3）

4. **剩余内容检查**：
   - ✅ 所有LIST类型片段保留到remaining_segments
   - ✅ 所有TABLE类型片段保留到remaining_segments
   - ✅ 所有CHART类型片段保留到remaining_segments
```

## 修复效果预期

通过以上修复，预期能够解决：

1. **类型混乱问题** - 严格限制只生成TITLE和TEXT控件
2. **编号跳跃问题** - 确保序列编号严格连续
3. **内容遗漏问题** - 完整处理所有应处理的内容片段
4. **处理范围问题** - 明确区分本步骤和后续步骤的职责

## 验证方法

使用修复后的提示词重新生成结果，检查：
- 是否只包含TITLE和TEXT控件
- 序列编号是否连续
- 是否处理了所有推荐为TITLE和TEXT的内容
- remaining_segments是否正确保留了其他类型的内容
